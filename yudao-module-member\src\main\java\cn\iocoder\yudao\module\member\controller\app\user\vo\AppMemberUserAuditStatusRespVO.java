package cn.iocoder.yudao.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 审核状态 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberUserAuditStatusRespVO {

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer auditStatus;

    @Schema(description = "审核状态名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "待审核")
    private String auditStatusName;

    @Schema(description = "审核时间", example = "2023-01-01 12:00:00")
    private LocalDateTime auditTime;

    @Schema(description = "审核备注", example = "请补充身份证信息")
    private String auditRemark;

    @Schema(description = "申请时间", example = "2023-01-01 10:00:00")
    private LocalDateTime applyTime;

}
