-- ----------------------------
-- 会员审核记录表
-- ----------------------------
DROP TABLE IF EXISTS `member_audit_record`;
CREATE TABLE `member_audit_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `audit_status` tinyint NOT NULL COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息',
  `audit_remark` varchar(500) NULL COMMENT '审核备注',
  `audit_time` datetime NOT NULL COMMENT '审核时间',
  `audit_user_id` bigint NOT NULL COMMENT '审核人ID',
  `audit_user_name` varchar(50) NULL COMMENT '审核人姓名',
  `before_status` tinyint NULL COMMENT '审核前状态',
  `after_status` tinyint NULL COMMENT '审核后状态',
  `creator` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE COMMENT '用户ID索引',
  INDEX `idx_audit_time` (`audit_time`) USING BTREE COMMENT '审核时间索引',
  INDEX `idx_audit_user_id` (`audit_user_id`) USING BTREE COMMENT '审核人ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员审核记录表';
