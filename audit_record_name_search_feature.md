# 审核记录按姓名查询功能实现

## 功能概述

为审核记录查询接口添加了按用户姓名和手机号查询的功能，支持模糊匹配。

## 修改的文件

### 1. 请求VO类
**文件**: `MemberAuditRecordPageReqVO.java`
- 添加了 `userName` 字段：用户姓名查询参数
- 添加了 `userMobile` 字段：用户手机号查询参数

### 2. 数据访问层
**文件**: `MemberAuditRecordMapper.java`
- 添加了 `selectPageByUserIds` 方法：支持按用户ID列表分页查询审核记录

**文件**: `MemberUserMapper.java`
- 添加了 `selectListByNameOrMobile` 方法：根据姓名或手机号查询用户列表

### 3. 服务层
**文件**: `MemberUserService.java`
- 添加了 `getUserIdsByNameOrMobile` 接口方法

**文件**: `MemberUserServiceImpl.java`
- 实现了 `getUserIdsByNameOrMobile` 方法

**文件**: `MemberAuditRecordServiceImpl.java`
- 修改了 `getAuditRecordPage` 方法，支持按姓名和手机号过滤

## 实现逻辑

### 查询流程
1. **检查查询条件**: 判断是否有用户姓名或手机号查询条件
2. **查询用户ID**: 如果有姓名/手机号条件，先查询符合条件的用户ID列表
3. **过滤审核记录**: 使用用户ID列表过滤审核记录
4. **返回结果**: 返回分页结果

### 查询逻辑
```java
// 如果有用户姓名或手机号查询条件
if (StrUtil.isNotBlank(pageReqVO.getUserName()) || StrUtil.isNotBlank(pageReqVO.getUserMobile())) {
    // 1. 查询符合条件的用户ID
    List<Long> userIds = memberUserService.getUserIdsByNameOrMobile(
        pageReqVO.getUserName(), 
        pageReqVO.getUserMobile()
    );
    
    // 2. 如果没有符合条件的用户，返回空结果
    if (userIds.isEmpty()) {
        return new PageResult<>(List.of(), 0L);
    }
    
    // 3. 按用户ID列表查询审核记录
    PageResult<MemberAuditRecordDO> pageResult = auditRecordMapper.selectPageByUserIds(newPageReqVO, userIds);
    return convertToRespVOPage(pageResult);
}
```

## API接口参数

### 分页查询接口
**接口**: `GET /member/audit-record/page`

**新增查询参数**:
- `userName`: 用户姓名（可选，支持模糊匹配）
- `userMobile`: 用户手机号（可选，支持模糊匹配）

**完整参数列表**:
```json
{
  "pageNo": 1,
  "pageSize": 10,
  "userId": 1024,
  "userName": "张三",
  "userMobile": "138",
  "auditStatus": 1,
  "auditUserId": 2048,
  "auditTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"]
}
```

## 使用示例

### 1. 按用户姓名查询
```bash
GET /member/audit-record/page?userName=张三&pageNo=1&pageSize=10
```

### 2. 按手机号查询
```bash
GET /member/audit-record/page?userMobile=138&pageNo=1&pageSize=10
```

### 3. 组合查询
```bash
GET /member/audit-record/page?userName=张&userMobile=138&auditStatus=1&pageNo=1&pageSize=10
```

## 特性说明

1. **模糊匹配**: 姓名和手机号都支持模糊匹配（LIKE查询）
2. **组合查询**: 可以同时使用姓名和手机号进行查询
3. **性能优化**: 先查询用户ID，再查询审核记录，避免复杂的关联查询
4. **空结果处理**: 如果没有符合条件的用户，直接返回空结果，避免无效查询
5. **兼容性**: 保持原有查询功能不变，新增功能向后兼容

## 注意事项

1. 姓名和手机号查询都是可选的，可以单独使用或组合使用
2. 查询条件为空时，不会影响其他查询条件的正常使用
3. 模糊匹配可能会影响查询性能，建议在用户表的姓名和手机号字段上建立索引
4. 查询结果会自动填充用户信息和审核人信息
