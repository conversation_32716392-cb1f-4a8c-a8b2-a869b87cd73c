package cn.iocoder.yudao.module.member.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 擅长画种枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SpecialtyPaintingEnum {

    /**
     * 国画
     */
    CHINESE_PAINTING("国画", "中国传统绘画"),
    
    /**
     * 油画
     */
    OIL_PAINTING("油画", "使用油性颜料绘制的画作"),
    
    /**
     * 水彩画
     */
    WATERCOLOR_PAINTING("水彩画", "使用水彩颜料绘制的画作"),
    
    /**
     * 水粉画
     */
    GOUACHE_PAINTING("水粉画", "使用水粉颜料绘制的画作"),
    
    /**
     * 素描
     */
    SKETCH("素描", "使用铅笔、炭笔等工具绘制的单色画"),
    
    /**
     * 速写
     */
    QUICK_SKETCH("速写", "快速记录形象的绘画方式"),
    
    /**
     * 版画
     */
    PRINTMAKING("版画", "通过版面制作的绘画作品"),
    
    /**
     * 书法
     */
    CALLIGRAPHY("书法", "中国传统文字艺术"),
    
    /**
     * 漫画
     */
    COMIC("漫画", "具有讽刺或幽默性的绘画"),
    
    /**
     * 插画
     */
    ILLUSTRATION("插画", "为文字内容配图的绘画"),
    
    /**
     * 工笔画
     */
    FINE_BRUSHWORK("工笔画", "中国画技法之一，注重细节描绘"),
    
    /**
     * 写意画
     */
    FREEHAND_BRUSHWORK("写意画", "中国画技法之一，注重意境表达"),
    
    /**
     * 丙烯画
     */
    ACRYLIC_PAINTING("丙烯画", "使用丙烯颜料绘制的画作"),
    
    /**
     * 彩铅画
     */
    COLORED_PENCIL("彩铅画", "使用彩色铅笔绘制的画作"),
    
    /**
     * 数字绘画
     */
    DIGITAL_PAINTING("数字绘画", "使用数字工具创作的绘画"),
    
    /**
     * 其他
     */
    OTHER("其他", "其他画种");

    /**
     * 画种名称
     */
    private final String name;
    
    /**
     * 画种描述
     */
    private final String description;

    /**
     * 根据名称获取枚举
     *
     * @param name 画种名称
     * @return 枚举
     */
    public static SpecialtyPaintingEnum getByName(String name) {
        for (SpecialtyPaintingEnum paintingEnum : values()) {
            if (paintingEnum.getName().equals(name)) {
                return paintingEnum;
            }
        }
        return null;
    }

    /**
     * 获取所有画种名称
     *
     * @return 画种名称数组
     */
    public static String[] getAllNames() {
        SpecialtyPaintingEnum[] values = values();
        String[] names = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            names[i] = values[i].getName();
        }
        return names;
    }
}
