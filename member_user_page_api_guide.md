# 会员用户分页接口使用指南

## 接口信息

- **接口路径**: `/member/user/page`
- **请求方式**: GET
- **权限要求**: `member:user:query`
- **功能描述**: 获得会员用户分页列表，支持多种查询条件

## 支持的查询条件

| 字段名 | 类型 | 查询方式 | 说明 | 示例 |
|--------|------|----------|------|------|
| mobile | String | 模糊查询 | 电话 | "156" |
| email | String | 模糊查询 | 邮箱 | "@example.com" |
| idCard | String | 模糊查询 | 身份证号 | "110101" |
| politicalStatus | Integer | 精确匹配 | 政治面貌 | 1 |
| workUnit | String | 模糊查询 | 工作单位 | "艺术学院" |
| specialtyPainting | String | 模糊查询 | 擅长画种 | "国画" |
| nickname | String | 模糊查询 | 用户昵称 | "张三" |
| name | String | 模糊查询 | 姓名 | "张" |
| loginStatus | Integer | 精确匹配 | 登录状态 | 1 |
| loginDate | LocalDateTime[] | 范围查询 | 最后登录时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |
| payTime | LocalDateTime[] | 范围查询 | 交费时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |
| expireTime | LocalDateTime[] | 范围查询 | 过期时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |
| createTime | LocalDateTime[] | 范围查询 | 创建时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |

## 枚举值参考

### 政治面貌 (politicalStatus)
| 值 | 名称 |
|----|------|
| 1 | 群众 |
| 2 | 共青团员 |
| 3 | 中共党员 |
| 4 | 中共预备党员 |
| 5 | 民革党员 |
| 6 | 民盟盟员 |
| 7 | 民建会员 |
| 8 | 民进会员 |
| 9 | 农工党党员 |
| 10 | 致公党党员 |
| 11 | 九三学社社员 |
| 12 | 台盟盟员 |
| 13 | 无党派人士 |

### 登录状态 (loginStatus)
| 值 | 名称 |
|----|------|
| 0 | 离线 |
| 1 | 在线 |

## 请求示例

### 1. 基础分页查询
```http
GET /member/user/page?pageNo=1&pageSize=10
```

### 2. 按姓名查询
```http
GET /member/user/page?pageNo=1&pageSize=10&name=张
```

### 3. 按电话查询
```http
GET /member/user/page?pageNo=1&pageSize=10&mobile=156
```

### 4. 按政治面貌查询
```http
GET /member/user/page?pageNo=1&pageSize=10&politicalStatus=1
```

### 5. 按登录状态查询
```http
GET /member/user/page?pageNo=1&pageSize=10&loginStatus=1
```

### 6. 按时间范围查询
```http
GET /member/user/page?pageNo=1&pageSize=10&createTime=2023-01-01 00:00:00,2023-12-31 23:59:59
```

### 7. 组合条件查询
```http
GET /member/user/page?pageNo=1&pageSize=10&name=张&mobile=156&politicalStatus=1&loginStatus=1
```

## 响应示例

```json
{
  "code": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 2025001,
        "mobile": "15601691300",
        "email": "<EMAIL>",
        "nickname": "用户123456",
        "name": "张三",
        "idCard": "1101**********1234",
        "politicalStatus": 1,
        "workUnit": "某某艺术学院",
        "specialtyPainting": "国画,油画",
        "sex": 1,
        "birthday": "1990-01-01 00:00:00",
        "avatar": "https://example.com/avatar.jpg",
        "status": 1,
        "loginStatus": 1,
        "auditStatus": 1,
        "auditTime": "2023-01-02 10:00:00",
        "payTime": "2023-01-03 10:00:00",
        "expireTime": "2024-01-03 10:00:00",
        "registerIp": "127.0.0.1",
        "loginIp": "127.0.0.1",
        "loginDate": "2023-01-10 10:00:00",
        "createTime": "2023-01-01 10:00:00"
      }
    ]
  },
  "msg": "操作成功"
}
```

## 查询逻辑说明

### 1. 模糊查询字段
以下字段支持模糊查询，会匹配包含关键词的记录：
- **电话**: 输入"156"会匹配"15601691300"、"15612345678"等
- **邮箱**: 输入"@example.com"会匹配所有example.com域名的邮箱
- **身份证号**: 输入"110101"会匹配以"110101"开头的身份证号
- **工作单位**: 输入"艺术学院"会匹配"某某艺术学院"、"北京艺术学院"等
- **擅长画种**: 输入"国画"会匹配"国画,油画"、"国画"等
- **昵称**: 输入"张"会匹配"张三"、"小张"等
- **姓名**: 输入"张"会匹配"张三"、"张小明"等

### 2. 精确匹配字段
- **政治面貌**: 必须完全匹配，1表示群众，2表示共青团员等
- **登录状态**: 必须完全匹配，0表示离线，1表示在线

### 3. 范围查询字段
- **最后登录时间**: 可以查询指定时间范围内登录的用户
- **交费时间**: 可以查询指定时间范围内交费的用户
- **过期时间**: 可以查询指定时间范围内过期的用户
- **创建时间**: 可以查询指定时间范围内注册的用户

### 4. 排序规则
- 默认按用户ID倒序排列
- 最新注册的用户显示在前面

## 与审核分页接口的区别

| 特性 | `/member/user/page` | `/member/user/audit-page` |
|------|---------------------|---------------------------|
| 用途 | 通用会员用户管理 | 专门用于审核管理 |
| 查询条件 | 更全面，包含所有用户字段 | 专注于审核相关字段 |
| 数据范围 | 所有状态的用户 | 主要是待审核和已审核用户 |
| 排序 | 按ID倒序 | 按申请时间倒序 |

## 前端集成建议

### 1. 查询表单设计
```html
<!-- 查询表单示例 -->
<form>
  <input type="text" placeholder="姓名" v-model="queryForm.name">
  <input type="text" placeholder="电话" v-model="queryForm.mobile">
  <input type="email" placeholder="邮箱" v-model="queryForm.email">
  <input type="text" placeholder="工作单位" v-model="queryForm.workUnit">
  <input type="text" placeholder="擅长画种" v-model="queryForm.specialtyPainting">
  <select v-model="queryForm.politicalStatus">
    <option value="">全部政治面貌</option>
    <option value="1">群众</option>
    <option value="2">共青团员</option>
    <option value="3">中共党员</option>
    <!-- 其他选项 -->
  </select>
  <select v-model="queryForm.loginStatus">
    <option value="">全部状态</option>
    <option value="0">离线</option>
    <option value="1">在线</option>
  </select>
  <date-range-picker v-model="queryForm.createTime" placeholder="注册时间">
  <date-range-picker v-model="queryForm.expireTime" placeholder="过期时间">
</form>
```

### 2. JavaScript调用示例
```javascript
// 查询函数
async function searchUsers(queryForm) {
  const params = new URLSearchParams({
    pageNo: 1,
    pageSize: 10,
    ...queryForm
  });
  
  const response = await axios.get(`/member/user/page?${params}`);
  return response.data;
}

// 使用示例
const result = await searchUsers({
  name: '张',
  politicalStatus: 1,
  loginStatus: 1
});
```

## 注意事项

1. **时间格式**: 时间参数需要使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **分页参数**: `pageNo` 从1开始，`pageSize` 建议设置为10、20、50等
3. **权限验证**: 需要确保用户具有 `member:user:query` 权限
4. **查询性能**: 建议在大数据量时适当限制查询条件，避免全表扫描
5. **身份证号**: 返回的身份证号已经脱敏处理，显示格式为 `1101**********1234`
6. **数据安全**: 接口返回的数据已经过脱敏处理，敏感信息不会完整显示

## 总结

`/member/user/page` 接口提供了完整的会员用户分页查询功能，支持多种查询条件和灵活的组合查询，适用于会员用户的日常管理和统计分析需求。
