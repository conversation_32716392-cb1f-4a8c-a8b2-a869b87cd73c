-- ========================================
-- 会员用户表(member_user)结构更新脚本
-- 更新日期: 2025-09-08
-- 说明: 根据新需求调整member_user表结构
-- ========================================

-- 1. 添加新字段
-- 添加身份证号字段
ALTER TABLE `member_user` ADD COLUMN `id_card` varchar(18) NULL COMMENT '身份证号' AFTER `name`;

-- 添加政治面貌字段
ALTER TABLE `member_user` ADD COLUMN `political_status` varchar(50) NULL COMMENT '政治面貌' AFTER `id_card`;

-- 添加邮箱字段
ALTER TABLE `member_user` ADD COLUMN `email` varchar(100) NULL COMMENT '邮箱' AFTER `mobile`;

-- 添加登录状态字段
ALTER TABLE `member_user` ADD COLUMN `login_status` tinyint NOT NULL DEFAULT 0 COMMENT '登录状态：0-离线，1-在线' AFTER `status`;

-- 添加审核相关字段（如果不存在）
ALTER TABLE `member_user` ADD COLUMN `audit_status` tinyint NULL COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息' AFTER `mark`;
ALTER TABLE `member_user` ADD COLUMN `audit_time` datetime NULL COMMENT '审核时间' AFTER `audit_status`;
ALTER TABLE `member_user` ADD COLUMN `audit_user_id` bigint NULL COMMENT '审核人ID' AFTER `audit_time`;
ALTER TABLE `member_user` ADD COLUMN `audit_remark` varchar(500) NULL COMMENT '审核备注' AFTER `audit_user_id`;
ALTER TABLE `member_user` ADD COLUMN `apply_time` datetime NULL COMMENT '申请时间（注册时间）' AFTER `audit_remark`;

-- 添加会员相关字段
ALTER TABLE `member_user` ADD COLUMN `pay_time` datetime NULL COMMENT '交费时间' AFTER `apply_time`;
ALTER TABLE `member_user` ADD COLUMN `expire_time` datetime NULL COMMENT '过期时间' AFTER `pay_time`;

-- 2. 修改现有字段注释
ALTER TABLE `member_user` MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会员卡号（自增）';
ALTER TABLE `member_user` MODIFY COLUMN `name` varchar(30) NULL COMMENT '姓名';
ALTER TABLE `member_user` MODIFY COLUMN `mobile` varchar(11) NOT NULL COMMENT '电话';
ALTER TABLE `member_user` MODIFY COLUMN `birthday` datetime NULL COMMENT '生日';
ALTER TABLE `member_user` MODIFY COLUMN `mark` varchar(255) NULL COMMENT '备注';

-- 3. 创建索引
-- 为身份证号创建唯一索引
CREATE UNIQUE INDEX `uk_id_card` ON `member_user` (`id_card`) COMMENT '身份证号唯一索引';

-- 为邮箱创建索引
CREATE INDEX `idx_email` ON `member_user` (`email`) COMMENT '邮箱索引';

-- 为审核状态创建索引
CREATE INDEX `idx_audit_status` ON `member_user` (`audit_status`) COMMENT '审核状态索引';

-- 为过期时间创建索引
CREATE INDEX `idx_expire_time` ON `member_user` (`expire_time`) COMMENT '过期时间索引';

-- 4. 如果表不存在，创建完整的member_user表
CREATE TABLE IF NOT EXISTS `member_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会员卡号（自增）',
  `nickname` varchar(30) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `name` varchar(30) NULL COMMENT '姓名',
  `id_card` varchar(18) NULL COMMENT '身份证号',
  `political_status` varchar(50) NULL COMMENT '政治面貌',
  `sex` tinyint NULL COMMENT '性别',
  `birthday` datetime NULL COMMENT '生日',
  `area_id` int NULL COMMENT '所在地',
  `mark` varchar(255) NULL COMMENT '备注',
  `point` int DEFAULT 0 NULL COMMENT '积分',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像',
  `status` tinyint NOT NULL COMMENT '状态',
  `login_status` tinyint NOT NULL DEFAULT 0 COMMENT '登录状态：0-离线，1-在线',
  `mobile` varchar(11) NOT NULL COMMENT '电话',
  `email` varchar(100) NULL COMMENT '邮箱',
  `password` varchar(100) NOT NULL DEFAULT '' COMMENT '密码',
  `register_ip` varchar(32) NOT NULL COMMENT '注册 IP',
  `register_terminal` tinyint NULL COMMENT '注册终端',
  `login_ip` varchar(50) NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `tag_ids` varchar(255) NULL DEFAULT NULL COMMENT '用户标签编号列表,以逗号分隔',
  `level_id` bigint NULL DEFAULT NULL COMMENT '等级编号',
  `experience` bigint NULL DEFAULT NULL COMMENT '经验',
  `group_id` bigint NULL DEFAULT NULL COMMENT '用户分组编号',
  `audit_status` tinyint NULL COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息',
  `audit_time` datetime NULL COMMENT '审核时间',
  `audit_user_id` bigint NULL COMMENT '审核人ID',
  `audit_remark` varchar(500) NULL COMMENT '审核备注',
  `apply_time` datetime NULL COMMENT '申请时间（注册时间）',
  `pay_time` datetime NULL COMMENT '交费时间',
  `expire_time` datetime NULL COMMENT '过期时间',
  `creator` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_mobile` (`mobile`) USING BTREE COMMENT '手机号唯一索引',
  UNIQUE KEY `uk_id_card` (`id_card`) USING BTREE COMMENT '身份证号唯一索引',
  KEY `idx_email` (`email`) USING BTREE COMMENT '邮箱索引',
  KEY `idx_audit_status` (`audit_status`) USING BTREE COMMENT '审核状态索引',
  KEY `idx_expire_time` (`expire_time`) USING BTREE COMMENT '过期时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员表';
