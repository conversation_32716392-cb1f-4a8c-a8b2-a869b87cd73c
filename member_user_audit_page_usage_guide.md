# 会员用户审核分页接口使用指南

## 接口信息

- **接口路径**: `/admin-api/member/user/audit-page`
- **请求方式**: POST
- **权限要求**: `member:user:query`

## 支持的查询条件

| 字段名 | 类型 | 查询方式 | 说明 | 示例 |
|--------|------|----------|------|------|
| name | String | 模糊查询 | 姓名 | "张三" |
| mobile | String | 模糊查询 | 电话 | "156" |
| email | String | 模糊查询 | 邮箱 | "@example.com" |
| workUnit | String | 模糊查询 | 工作单位 | "艺术学院" |
| specialtyPainting | String | 模糊查询 | 擅长画种 | "国画" |
| auditStatus | Integer | 精确匹配 | 审核状态 | 0 |
| applyTime | LocalDateTime[] | 范围查询 | 提交时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |
| auditTime | LocalDateTime[] | 范围查询 | 审核时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |

## 审核状态枚举值

| 值 | 名称 | 说明 |
|----|------|------|
| 0 | 待审核 | 用户提交申请，等待审核 |
| 1 | 审核通过 | 审核通过，可正常使用 |
| 2 | 审核拒绝 | 审核不通过 |
| 3 | 需要补充信息 | 需要用户补充更多信息 |

## 请求示例

### 1. 基础分页查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json
Authorization: Bearer your-token

{
  "pageNo": 1,
  "pageSize": 10
}
```

### 2. 按姓名查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "name": "张"
}
```

### 3. 按电话查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "mobile": "156"
}
```

### 4. 按审核状态查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "auditStatus": 0
}
```

### 5. 按时间范围查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "applyTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"],
  "auditTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"]
}
```

### 6. 组合条件查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "name": "张",
  "mobile": "156",
  "email": "@example.com",
  "workUnit": "艺术学院",
  "specialtyPainting": "国画",
  "auditStatus": 0,
  "applyTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"]
}
```

## 响应示例

```json
{
  "code": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 2025001,
        "mobile": "15601691300",
        "nickname": "用户123456",
        "name": "张三",
        "email": "<EMAIL>",
        "idCard": "1101**********1234",
        "politicalStatus": 1,
        "workUnit": "某某艺术学院",
        "specialtyPainting": "国画,油画",
        "sex": 1,
        "avatar": "https://example.com/avatar.jpg",
        "auditStatus": 0,
        "auditStatusName": "待审核",
        "auditTime": null,
        "auditUserId": null,
        "auditRemark": null,
        "applyTime": "2023-01-01 10:00:00",
        "registerIp": "127.0.0.1",
        "createTime": "2023-01-01 10:00:00"
      }
    ]
  },
  "msg": "操作成功"
}
```

## 查询逻辑说明

### 1. 模糊查询字段
以下字段支持模糊查询，会匹配包含关键词的记录：
- **姓名**: 输入"张"会匹配"张三"、"张小明"等
- **电话**: 输入"156"会匹配"15601691300"、"15612345678"等
- **邮箱**: 输入"@example.com"会匹配所有example.com域名的邮箱
- **工作单位**: 输入"艺术学院"会匹配"某某艺术学院"、"北京艺术学院"等
- **擅长画种**: 输入"国画"会匹配"国画,油画"、"国画"等

### 2. 精确匹配字段
- **审核状态**: 必须完全匹配，0表示待审核，1表示审核通过等

### 3. 范围查询字段
- **提交时间**: 可以查询指定时间范围内提交的申请
- **审核时间**: 可以查询指定时间范围内审核的申请

### 4. 排序规则
- 默认按提交时间（applyTime）倒序排列
- 最新提交的申请显示在前面

## 前端集成建议

### 1. 查询表单设计
```html
<!-- 查询表单示例 -->
<form>
  <input type="text" placeholder="姓名" v-model="queryForm.name">
  <input type="text" placeholder="电话" v-model="queryForm.mobile">
  <input type="email" placeholder="邮箱" v-model="queryForm.email">
  <input type="text" placeholder="工作单位" v-model="queryForm.workUnit">
  <input type="text" placeholder="擅长画种" v-model="queryForm.specialtyPainting">
  <select v-model="queryForm.auditStatus">
    <option value="">全部状态</option>
    <option value="0">待审核</option>
    <option value="1">审核通过</option>
    <option value="2">审核拒绝</option>
    <option value="3">需要补充信息</option>
  </select>
  <date-range-picker v-model="queryForm.applyTime" placeholder="提交时间">
  <date-range-picker v-model="queryForm.auditTime" placeholder="审核时间">
</form>
```

### 2. JavaScript调用示例
```javascript
// 查询函数
async function searchAuditUsers(queryForm) {
  const response = await axios.post('/admin-api/member/user/audit-page', {
    pageNo: 1,
    pageSize: 10,
    ...queryForm
  });
  return response.data;
}

// 使用示例
const result = await searchAuditUsers({
  name: '张',
  auditStatus: 0,
  applyTime: ['2023-01-01 00:00:00', '2023-12-31 23:59:59']
});
```

## 注意事项

1. **时间格式**: 时间参数需要使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **分页参数**: `pageNo` 从1开始，`pageSize` 建议设置为10、20、50等
3. **权限验证**: 需要确保用户具有 `member:user:query` 权限
4. **查询性能**: 建议在大数据量时适当限制查询条件，避免全表扫描
5. **身份证号**: 返回的身份证号已经脱敏处理，显示格式为 `1101**********1234`

## 总结

当前的审核分页接口已经完全支持您要求的所有查询条件，无需进行任何修改。接口设计合理，支持灵活的组合查询，能够满足各种审核管理场景的需求。
