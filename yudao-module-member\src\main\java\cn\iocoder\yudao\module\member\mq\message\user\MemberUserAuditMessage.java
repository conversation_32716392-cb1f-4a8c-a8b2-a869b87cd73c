package cn.iocoder.yudao.module.member.mq.message.user;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 会员用户审核消息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MemberUserAuditMessage {

    /**
     * 用户编号
     */
    private Long userId;
    
    /**
     * 审核状态
     */
    private Integer auditStatus;
    
    /**
     * 审核备注
     */
    private String auditRemark;

}
