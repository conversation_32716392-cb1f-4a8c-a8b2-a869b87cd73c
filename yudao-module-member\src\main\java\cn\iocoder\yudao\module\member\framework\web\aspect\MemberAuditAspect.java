package cn.iocoder.yudao.module.member.framework.web.aspect;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum;
import cn.iocoder.yudao.module.member.framework.web.annotation.RequireAuditApproved;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.member.enums.ErrorCodeConstants.USER_NOT_APPROVED;

/**
 * 会员审核状态检查切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class MemberAuditAspect {

    @Resource
    private MemberUserService memberUserService;

    @Around("@annotation(requireAuditApproved) || @within(requireAuditApproved)")
    public Object around(ProceedingJoinPoint joinPoint, RequireAuditApproved requireAuditApproved) throws Throwable {
        // 如果不需要审核检查，直接执行
        if (requireAuditApproved != null && !requireAuditApproved.value()) {
            return joinPoint.proceed();
        }

        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId == null) {
            // 未登录用户，直接执行（由其他安全机制处理）
            return joinPoint.proceed();
        }

        // 检查用户审核状态
        MemberUserDO user = memberUserService.getUser(userId);
        if (user == null) {
            // 用户不存在，直接执行（由其他机制处理）
            return joinPoint.proceed();
        }

        // 检查审核状态
        MemberAuditStatusEnum auditStatus = MemberAuditStatusEnum.getByStatus(user.getAuditStatus());
        if (auditStatus != MemberAuditStatusEnum.APPROVED) {
            log.warn("[around][用户未通过审核，userId：{}，auditStatus：{}]", userId, auditStatus);
            throw exception(USER_NOT_APPROVED);
        }

        // 审核通过，执行原方法
        return joinPoint.proceed();
    }

}
