# 会员用户表最终结构修改总结

## 修改概述

根据最新需求，重新整理了member_user表的字段结构，主要包含以下字段：
- **会员卡号（自增）** - id字段
- **姓名** - name字段
- **身份证号** - id_card字段（**加密存储**）
- **政治面貌** - political_status字段（**枚举值**）
- **电话** - mobile字段
- **邮箱** - email字段
- **生日** - birthday字段
- **密码** - password字段（**加密存储**）
- **工作单位** - work_unit字段
- **擅长画种** - specialty_painting字段
- **审核状态** - audit_status字段（**枚举值**）
- **审核时间** - audit_time字段
- **交费时间** - pay_time字段
- **过期时间** - expire_time字段
- **登录状态** - login_status字段（**枚举值**）

## 修改的文件列表

### 1. 数据库相关文件

#### 1.1 SQL脚本
- `sql/mysql/member_user_complete_structure.sql` - 新建，完整的表结构脚本
- `sql/mysql/member_user_update_structure.sql` - 新建，增量更新脚本
- `yudao-module-member/src/test/resources/sql/create_tables.sql` - 更新测试用建表语句

#### 1.2 实体类
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/dal/dataobject/user/MemberUserDO.java` - 更新字段类型

### 2. 枚举类修改

#### 2.1 更新的枚举
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/PoliticalStatusEnum.java` - 修改为数字枚举
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/MemberLoginStatusEnum.java` - 登录状态枚举（已存在）
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/MemberAuditStatusEnum.java` - 审核状态枚举（已存在）

### 3. 工具类

#### 3.1 新增工具类
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/util/MemberEncryptUtils.java` - 新建，加密工具类

### 4. 业务层修改

#### 4.1 Service实现类
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/service/user/MemberUserServiceImpl.java` - 添加加密逻辑和验证

#### 4.2 转换器
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/convert/user/MemberUserConvert.java` - 添加解密逻辑

### 5. 控制层相关文件

#### 5.1 所有VO类
- 所有包含政治面貌字段的VO类都已更新为Integer类型

### 6. 错误码
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/ErrorCodeConstants.java` - 添加身份证号格式错误码

## 枚举值定义

### 1. 政治面貌枚举 (PoliticalStatusEnum)

| 值 | 名称 | 说明 |
|----|------|------|
| 1 | 群众 | 普通群众 |
| 2 | 共青团员 | 中国共产主义青年团团员 |
| 3 | 中共党员 | 中国共产党党员 |
| 4 | 中共预备党员 | 中国共产党预备党员 |
| 5 | 民革党员 | 中国国民党革命委员会党员 |
| 6 | 民盟盟员 | 中国民主同盟盟员 |
| 7 | 民建会员 | 中国民主建国会会员 |
| 8 | 民进会员 | 中国民主促进会会员 |
| 9 | 农工党党员 | 中国农工民主党党员 |
| 10 | 致公党党员 | 中国致公党党员 |
| 11 | 九三学社社员 | 九三学社社员 |
| 12 | 台盟盟员 | 台湾民主自治同盟盟员 |
| 13 | 无党派人士 | 无党派人士 |

### 2. 审核状态枚举 (MemberAuditStatusEnum)

| 值 | 名称 | 说明 |
|----|------|------|
| 0 | 待审核 | 用户提交申请，等待审核 |
| 1 | 审核通过 | 审核通过，可正常使用 |
| 2 | 审核拒绝 | 审核不通过 |
| 3 | 需要补充信息 | 需要用户补充更多信息 |

### 3. 登录状态枚举 (MemberLoginStatusEnum)

| 值 | 名称 | 说明 |
|----|------|------|
| 0 | 离线 | 用户当前离线 |
| 1 | 在线 | 用户当前在线 |

## 加密功能

### 1. 身份证号加密

#### 1.1 加密方式
- 使用AES对称加密
- 默认密钥：`YuDao2024Member!`
- 可通过配置文件 `member.encrypt.key` 自定义密钥

#### 1.2 加密流程
1. **注册时**：用户输入原始身份证号 → 格式验证 → 唯一性验证 → AES加密 → 存储到数据库
2. **查询时**：从数据库读取加密数据 → AES解密 → 返回给前端
3. **显示时**：提供脱敏显示功能（显示前4位和后4位）

#### 1.3 相关方法
- `encryptIdCard(String idCard)` - 加密身份证号
- `decryptIdCard(String encryptedIdCard)` - 解密身份证号
- `maskIdCard(String idCard)` - 脱敏显示
- `isValidIdCard(String idCard)` - 格式验证

### 2. 密码加密
- 继续使用BCrypt加密（原有逻辑不变）

## 数据库结构变更

### 1. 字段类型变更

| 字段名 | 原类型 | 新类型 | 说明 |
|--------|--------|--------|------|
| id_card | varchar(18) | varchar(255) | 加密后长度增加 |
| political_status | varchar(50) | tinyint | 改为数字枚举 |
| password | varchar(100) | varchar(100) | 注释更新为加密存储 |

### 2. 新增索引

| 索引名 | 字段 | 类型 | 说明 |
|--------|------|------|------|
| idx_political_status | political_status | INDEX | 政治面貌索引 |
| idx_work_unit | work_unit | INDEX | 工作单位索引 |
| idx_specialty_painting | specialty_painting | INDEX | 擅长画种索引 |

## 业务流程影响

### 1. 注册流程

#### 1.1 前端变更
- 政治面貌字段改为下拉选择（数字值）
- 身份证号输入需要格式验证

#### 1.2 后端处理
1. 接收注册数据
2. 验证身份证号格式
3. 验证各字段唯一性（身份证号需先加密再查询）
4. 身份证号AES加密存储
5. 密码BCrypt加密存储
6. 设置默认状态值

### 2. 会员信息获取流程

#### 2.1 数据返回
- 身份证号自动解密后返回
- 政治面貌返回数字值，前端需要转换为文字显示
- 提供脱敏显示选项

#### 2.2 查询功能
- 支持按政治面貌、工作单位、擅长画种等字段查询
- 身份证号查询需要先加密再匹配

## 使用示例

### 1. 用户注册
```java
AppAuthRegisterReqVO reqVO = new AppAuthRegisterReqVO();
reqVO.setMobile("15601691300");
reqVO.setPassword("123456");
reqVO.setName("张三");
reqVO.setIdCard("110101199001011234"); // 原始身份证号
reqVO.setPoliticalStatus(1); // 1-群众
reqVO.setWorkUnit("某某艺术学院");
reqVO.setSpecialtyPainting("国画,油画,书法");

Long userId = memberUserService.createUser(reqVO, "127.0.0.1", 1);
```

### 2. 获取用户信息
```java
// 获取用户信息（身份证号会自动解密）
AppMemberUserInfoRespVO userInfo = memberUserService.getUserInfo(userId);
System.out.println("身份证号: " + userInfo.getIdCard()); // 已解密的原始身份证号
System.out.println("政治面貌: " + userInfo.getPoliticalStatus()); // 数字值，如：1

// 脱敏显示
String maskedIdCard = memberEncryptUtils.maskIdCard(userInfo.getIdCard());
System.out.println("脱敏身份证号: " + maskedIdCard); // 1101**********1234
```

### 3. 政治面貌转换
```java
// 数字转文字
Integer politicalStatus = 1;
PoliticalStatusEnum statusEnum = PoliticalStatusEnum.getByStatus(politicalStatus);
String statusName = statusEnum.getName(); // "群众"

// 文字转数字
String statusName = "群众";
PoliticalStatusEnum statusEnum = PoliticalStatusEnum.getByName(statusName);
Integer statusValue = statusEnum.getStatus(); // 1
```

## 数据库更新步骤

### 1. 方式一：增量更新（推荐）
```sql
-- 执行增量更新脚本
source sql/mysql/member_user_update_structure.sql;
```

### 2. 方式二：重建表（谨慎使用）
```sql
-- 备份现有数据
CREATE TABLE member_user_backup AS SELECT * FROM member_user;

-- 执行完整结构脚本
source sql/mysql/member_user_complete_structure.sql;

-- 迁移数据（需要处理字段类型变更）
```

## 注意事项

1. **数据迁移**：
   - 现有的政治面貌数据需要从文字转换为数字
   - 现有的身份证号数据需要加密处理

2. **配置文件**：
   - 建议在生产环境配置自定义加密密钥
   - 密钥一旦设置不可随意更改

3. **性能考虑**：
   - 身份证号查询需要加密后匹配，可能影响查询性能
   - 建议对常用查询字段建立索引

4. **安全性**：
   - 加密密钥需要妥善保管
   - 日志中避免输出敏感信息

5. **前端适配**：
   - 政治面貌字段需要更新为下拉选择
   - 身份证号显示可选择脱敏模式
   - 错误提示需要处理新的错误码
