-- ========================================
-- 会员用户表(member_user)结构更新脚本
-- 更新日期: 2025-09-09
-- 说明: 更新现有member_user表结构，添加缺失字段和修改字段类型
-- ========================================

-- 1. 添加缺失的字段（如果不存在）
ALTER TABLE `member_user` ADD COLUMN IF NOT EXISTS `work_unit` varchar(200) NULL COMMENT '工作单位' AFTER `birthday`;
ALTER TABLE `member_user` ADD COLUMN IF NOT EXISTS `specialty_painting` varchar(500) NULL COMMENT '擅长画种' AFTER `work_unit`;

-- 2. 修改现有字段类型和注释
-- 修改身份证号字段为加密存储
ALTER TABLE `member_user` MODIFY COLUMN `id_card` varchar(255) NULL COMMENT '身份证号（加密存储）';

-- 修改密码字段注释
ALTER TABLE `member_user` MODIFY COLUMN `password` varchar(100) NOT NULL COMMENT '密码（加密存储）';

-- 修改政治面貌字段为枚举值
ALTER TABLE `member_user` MODIFY COLUMN `political_status` tinyint NULL COMMENT '政治面貌：1-群众，2-共青团员，3-中共党员，4-中共预备党员，5-民革党员，6-民盟盟员，7-民建会员，8-民进会员，9-农工党党员，10-致公党党员，11-九三学社社员，12-台盟盟员，13-无党派人士';

-- 修改审核状态字段注释
ALTER TABLE `member_user` MODIFY COLUMN `audit_status` tinyint NULL COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息';

-- 修改登录状态字段注释
ALTER TABLE `member_user` MODIFY COLUMN `login_status` tinyint NOT NULL DEFAULT 0 COMMENT '登录状态：0-离线，1-在线';

-- 3. 创建索引（如果不存在）
-- 政治面貌索引
CREATE INDEX IF NOT EXISTS `idx_political_status` ON `member_user` (`political_status`) COMMENT '政治面貌索引';

-- 工作单位索引
CREATE INDEX IF NOT EXISTS `idx_work_unit` ON `member_user` (`work_unit`) COMMENT '工作单位索引';

-- 擅长画种索引
CREATE INDEX IF NOT EXISTS `idx_specialty_painting` ON `member_user` (`specialty_painting`) COMMENT '擅长画种索引';

-- 5. 设置自增起始值为2025000
ALTER TABLE `member_user` AUTO_INCREMENT = 2025000;

-- 4. 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'member_user' 
-- AND COLUMN_NAME IN ('work_unit', 'specialty_painting', 'political_status', 'audit_status', 'login_status', 'id_card')
-- ORDER BY ORDINAL_POSITION;
