package cn.iocoder.yudao.module.member.dal.mysql.auditrecord;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 会员审核记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberAuditRecordMapper extends BaseMapperX<MemberAuditRecordDO> {

    default PageResult<MemberAuditRecordDO> selectPage(MemberAuditRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberAuditRecordDO>()
                .eqIfPresent(MemberAuditRecordDO::getUserId, reqVO.getUserId())
                .eqIfPresent(MemberAuditRecordDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(MemberAuditRecordDO::getAuditUserId, reqVO.getAuditUserId())
                .betweenIfPresent(MemberAuditRecordDO::getAuditTime, reqVO.getAuditTime())
                .orderByDesc(MemberAuditRecordDO::getAuditTime));
    }

    default List<MemberAuditRecordDO> selectListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<MemberAuditRecordDO>()
                .eq(MemberAuditRecordDO::getUserId, userId)
                .orderByDesc(MemberAuditRecordDO::getAuditTime));
    }

}
