package cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员审核记录 Response VO")
@Data
public class MemberAuditRecordRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    private Long userId;

    @Schema(description = "用户姓名", example = "张三")
    private String userName;

    @Schema(description = "用户手机号", example = "13888888888")
    private String userMobile;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer auditStatus;

    @Schema(description = "审核状态名称", example = "审核通过")
    private String auditStatusName;

    @Schema(description = "审核备注", example = "审核通过")
    private String auditRemark;

    @Schema(description = "审核时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime auditTime;

    @Schema(description = "审核人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long auditUserId;

    @Schema(description = "审核人姓名", example = "管理员")
    private String auditUserName;

    @Schema(description = "审核前状态", example = "0")
    private Integer beforeStatus;

    @Schema(description = "审核前状态名称", example = "待审核")
    private String beforeStatusName;

    @Schema(description = "审核后状态", example = "1")
    private Integer afterStatus;

    @Schema(description = "审核后状态名称", example = "审核通过")
    private String afterStatusName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
