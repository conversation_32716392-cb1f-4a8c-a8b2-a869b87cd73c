package cn.iocoder.yudao.module.member.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.AES;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 会员信息加密工具类
 * 用于加密敏感信息如身份证号等
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MemberEncryptUtils {

    /**
     * AES加密密钥，建议从配置文件读取
     */
    private static final String DEFAULT_KEY = "YuDao2024Member!";
    
    @Value("${member.encrypt.key:#{null}}")
    private String encryptKey;
    
    private AES aes;
    
    /**
     * 获取AES实例
     */
    private AES getAes() {
        if (aes == null) {
            String key = StrUtil.isNotBlank(encryptKey) ? encryptKey : DEFAULT_KEY;
            aes = new AES(key.getBytes());
        }
        return aes;
    }
    
    /**
     * 加密身份证号
     *
     * @param idCard 原始身份证号
     * @return 加密后的身份证号
     */
    public String encryptIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return idCard;
        }
        try {
            return getAes().encryptHex(idCard);
        } catch (Exception e) {
            log.error("身份证号加密失败", e);
            throw new RuntimeException("身份证号加密失败", e);
        }
    }
    
    /**
     * 解密身份证号
     *
     * @param encryptedIdCard 加密后的身份证号
     * @return 原始身份证号
     */
    public String decryptIdCard(String encryptedIdCard) {
        if (StrUtil.isBlank(encryptedIdCard)) {
            return encryptedIdCard;
        }
        try {
            return getAes().decryptStr(encryptedIdCard);
        } catch (Exception e) {
            log.error("身份证号解密失败", e);
            throw new RuntimeException("身份证号解密失败", e);
        }
    }
    
    /**
     * 身份证号脱敏显示
     * 显示前4位和后4位，中间用*代替
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public String maskIdCard(String idCard) {
        if (StrUtil.isBlank(idCard) || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
    
    /**
     * 验证身份证号格式
     *
     * @param idCard 身份证号
     * @return 是否有效
     */
    public boolean isValidIdCard(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return false;
        }
        // 18位身份证号正则表达式
        String regex = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return idCard.matches(regex);
    }
    
    /**
     * 静态方法：加密身份证号
     * 用于在没有Spring容器的环境中使用
     */
    public static String encryptIdCardStatic(String idCard) {
        if (StrUtil.isBlank(idCard)) {
            return idCard;
        }
        try {
            AES aes = new AES(DEFAULT_KEY.getBytes());
            return aes.encryptHex(idCard);
        } catch (Exception e) {
            log.error("身份证号加密失败", e);
            throw new RuntimeException("身份证号加密失败", e);
        }
    }
    
    /**
     * 静态方法：解密身份证号
     * 用于在没有Spring容器的环境中使用
     */
    public static String decryptIdCardStatic(String encryptedIdCard) {
        if (StrUtil.isBlank(encryptedIdCard)) {
            return encryptedIdCard;
        }
        try {
            AES aes = new AES(DEFAULT_KEY.getBytes());
            return aes.decryptStr(encryptedIdCard);
        } catch (Exception e) {
            log.error("身份证号解密失败", e);
            throw new RuntimeException("身份证号解密失败", e);
        }
    }

    /**
     * 静态方法：身份证号脱敏显示
     * 显示前4位和后4位，中间用*代替
     * 用于在没有Spring容器的环境中使用
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdCardStatic(String idCard) {
        if (StrUtil.isBlank(idCard) || idCard.length() < 8) {
            return idCard;
        }
        return idCard.substring(0, 4) + "**********" + idCard.substring(idCard.length() - 4);
    }
}
