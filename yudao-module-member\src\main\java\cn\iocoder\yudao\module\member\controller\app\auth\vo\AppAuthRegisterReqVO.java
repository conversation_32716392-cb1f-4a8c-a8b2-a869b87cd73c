package cn.iocoder.yudao.module.member.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.*;
import java.time.LocalDate;

@Schema(description = "用户 APP - 注册 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppAuthRegisterReqVO {

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 20, message = "密码长度为 6-20 位")
    private String password;

    @Schema(description = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotBlank(message = "短信验证码不能为空")
    @Length(min = 4, max = 6, message = "短信验证码长度为 4-6 位")
    private String code;

    @Schema(description = "用户昵称", example = "张三")
    @Length(max = 30, message = "用户昵称长度不能超过30位")
    private String nickname;

    @Schema(description = "真实姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "真实名字不能为空")
    @Length(max = 30, message = "真实姓名长度不能超过30位")
    private String name;

    @Schema(description = "性别", example = "1")
    private Integer sex;

    @Schema(description = "生日", example = "1990-01-01", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生日不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @Schema(description = "头像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "邮箱", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "邮箱不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", message = "邮箱格式不正确")
    @Length(max = 100, message = "邮箱长度不能超过100位")
    private String email;

    @Schema(description = "身份证号", example = "110101199001011234", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", message = "身份证号格式不正确")
    @Length(max = 18, message = "身份证号长度不能超过18位")
    private String idCard;

    @Schema(description = "政治面貌", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "政治面貌不能为空")
    @Min(value = 1, message = "政治面貌值必须在1-13之间")
    @Max(value = 13, message = "政治面貌值必须在1-13之间")
    private Integer politicalStatus;

    @Schema(description = "工作单位", example = "某某公司", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工作单位不能为空")
    @Length(max = 200, message = "工作单位长度不能超过200位")
    private String workUnit;

    @Schema(description = "擅长画种", example = "国画,油画,水彩画", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "擅长画种不能为空")
    @Length(max = 500, message = "擅长画种长度不能超过500位")
    private String specialtyPainting;

}
