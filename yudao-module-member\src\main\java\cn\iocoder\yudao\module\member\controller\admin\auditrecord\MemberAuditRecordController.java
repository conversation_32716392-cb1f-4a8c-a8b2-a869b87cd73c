package cn.iocoder.yudao.module.member.controller.admin.auditrecord;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.convert.auditrecord.MemberAuditRecordConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.service.auditrecord.MemberAuditRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员审核记录")
@RestController
@RequestMapping("/member/audit-record")
@Validated
public class MemberAuditRecordController {

    @Resource
    private MemberAuditRecordService auditRecordService;

    @GetMapping("/page")
    @Operation(summary = "获得会员审核记录分页")
    @PreAuthorize("@ss.hasPermission('member:audit-record:query')")
    public CommonResult<PageResult<MemberAuditRecordRespVO>> getAuditRecordPage(@Valid MemberAuditRecordPageReqVO pageReqVO) {
        PageResult<MemberAuditRecordDO> pageResult = auditRecordService.getAuditRecordPage(pageReqVO);
        return success(MemberAuditRecordConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/list-by-user-id")
    @Operation(summary = "获得指定用户的审核记录列表")
    @Parameter(name = "userId", description = "用户编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:audit-record:query')")
    public CommonResult<List<MemberAuditRecordRespVO>> getAuditRecordListByUserId(@RequestParam("userId") Long userId) {
        List<MemberAuditRecordDO> list = auditRecordService.getAuditRecordListByUserId(userId);
        return success(MemberAuditRecordConvert.INSTANCE.convertList(list));
    }

}
