package cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员审核记录创建 Request VO")
@Data
public class MemberAuditRecordCreateReqVO {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审核备注", example = "审核通过")
    private String auditRemark;

    @Schema(description = "审核时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "审核时间不能为空")
    private LocalDateTime auditTime;

    @Schema(description = "审核人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "审核人ID不能为空")
    private Long auditUserId;

    @Schema(description = "审核人姓名", example = "管理员")
    private String auditUserName;

    @Schema(description = "审核前状态", example = "0")
    private Integer beforeStatus;

    @Schema(description = "审核后状态", example = "1")
    private Integer afterStatus;

}
