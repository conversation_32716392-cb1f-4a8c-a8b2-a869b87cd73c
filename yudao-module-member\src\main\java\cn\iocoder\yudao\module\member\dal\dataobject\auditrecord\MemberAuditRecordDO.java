package cn.iocoder.yudao.module.member.dal.dataobject.auditrecord;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 会员审核记录 DO
 *
 * <AUTHOR>
 */
@TableName("member_audit_record")
@KeySequence("member_audit_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberAuditRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 审核状态
     * 
     * 枚举 {@link cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum}
     */
    private Integer auditStatus;
    
    /**
     * 审核备注
     */
    private String auditRemark;
    
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    
    /**
     * 审核人ID
     */
    private Long auditUserId;
    
    /**
     * 审核人姓名
     */
    private String auditUserName;
    
    /**
     * 审核前状态
     * 
     * 枚举 {@link cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum}
     */
    private Integer beforeStatus;
    
    /**
     * 审核后状态
     * 
     * 枚举 {@link cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum}
     */
    private Integer afterStatus;

}
