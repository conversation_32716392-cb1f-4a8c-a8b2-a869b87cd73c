package cn.iocoder.yudao.module.member.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员登录状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MemberLoginStatusEnum {

    /**
     * 离线
     */
    OFFLINE(0, "离线"),
    
    /**
     * 在线
     */
    ONLINE(1, "在线");

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static MemberLoginStatusEnum getByStatus(Integer status) {
        for (MemberLoginStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
