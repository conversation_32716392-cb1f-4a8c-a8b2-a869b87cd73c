package cn.iocoder.yudao.module.member.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MemberAuditStatusEnum {

    /**
     * 待审核
     */
    PENDING(0, "待审核"),
    
    /**
     * 审核通过
     */
    APPROVED(1, "审核通过"),
    
    /**
     * 审核拒绝
     */
    REJECTED(2, "审核拒绝"),
    
    /**
     * 需要补充信息
     */
    NEED_SUPPLEMENT(3, "需要补充信息");

    /**
     * 状态值
     */
    private final Integer status;
    
    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static MemberAuditStatusEnum getByStatus(Integer status) {
        for (MemberAuditStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
