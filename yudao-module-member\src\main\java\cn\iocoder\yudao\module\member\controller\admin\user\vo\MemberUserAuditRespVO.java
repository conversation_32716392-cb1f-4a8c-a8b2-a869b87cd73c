package cn.iocoder.yudao.module.member.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 会员用户审核 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MemberUserAuditRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    private String mobile;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "真实姓名", example = "张三")
    private String name;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "身份证号", example = "110101199001011234")
    private String idCard;

    @Schema(description = "政治面貌", example = "1")
    private Integer politicalStatus;

    @Schema(description = "工作单位", example = "某某公司")
    private String workUnit;

    @Schema(description = "擅长画种", example = "国画,油画,水彩画")
    private String specialtyPainting;

    @Schema(description = "性别", example = "1")
    private Integer sex;

    @Schema(description = "头像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer auditStatus;

    @Schema(description = "审核状态名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "待审核")
    private String auditStatusName;

    @Schema(description = "审核时间", example = "2023-01-01 12:00:00")
    private LocalDateTime auditTime;

    @Schema(description = "审核人ID", example = "1")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "请补充身份证信息")
    private String auditRemark;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-01-01 10:00:00")
    private LocalDateTime applyTime;

    @Schema(description = "注册IP", example = "127.0.0.1")
    private String registerIp;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
