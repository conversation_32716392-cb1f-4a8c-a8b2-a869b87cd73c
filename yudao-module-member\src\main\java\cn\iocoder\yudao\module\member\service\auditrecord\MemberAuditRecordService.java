package cn.iocoder.yudao.module.member.service.auditrecord;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员审核记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberAuditRecordService {

    /**
     * 创建会员审核记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAuditRecord(@Valid MemberAuditRecordCreateReqVO createReqVO);

    /**
     * 获得会员审核记录分页
     *
     * @param pageReqVO 分页查询
     * @return 会员审核记录分页
     */
    PageResult<MemberAuditRecordRespVO> getAuditRecordPage(MemberAuditRecordPageReqVO pageReqVO);

    /**
     * 获得指定用户的审核记录列表
     *
     * @param userId 用户编号
     * @return 审核记录列表
     */
    List<MemberAuditRecordRespVO> getAuditRecordListByUserId(Long userId);

}
