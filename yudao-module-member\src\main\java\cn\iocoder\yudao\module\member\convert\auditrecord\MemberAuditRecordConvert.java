package cn.iocoder.yudao.module.member.convert.auditrecord;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 会员审核记录 Convert
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public abstract class MemberAuditRecordConvert {

    public static final MemberAuditRecordConvert INSTANCE = Mappers.getMapper(MemberAuditRecordConvert.class);

    @Autowired
    private MemberUserService memberUserService;

    @Autowired
    private AdminUserApi adminUserApi;

    public abstract MemberAuditRecordDO convert(MemberAuditRecordCreateReqVO bean);

    @Mapping(target = "auditStatusName", expression = "java(getAuditStatusName(bean.getAuditStatus()))")
    @Mapping(target = "beforeStatusName", expression = "java(getAuditStatusName(bean.getBeforeStatus()))")
    @Mapping(target = "afterStatusName", expression = "java(getAuditStatusName(bean.getAfterStatus()))")
    @Mapping(target = "userName", expression = "java(getUserName(bean.getUserId()))")
    @Mapping(target = "userMobile", expression = "java(getUserMobile(bean.getUserId()))")
    @Mapping(target = "auditUserName", expression = "java(getAuditUserName(bean.getAuditUserId()))")
    public abstract MemberAuditRecordRespVO convert(MemberAuditRecordDO bean);

    public abstract List<MemberAuditRecordRespVO> convertList(List<MemberAuditRecordDO> list);

    public abstract PageResult<MemberAuditRecordRespVO> convertPage(PageResult<MemberAuditRecordDO> page);

    protected String getAuditStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        MemberAuditStatusEnum statusEnum = MemberAuditStatusEnum.getByStatus(status);
        return statusEnum != null ? statusEnum.getName() : null;
    }

    protected String getUserName(Long userId) {
        if (userId == null) {
            return null;
        }
        MemberUserDO user = memberUserService.getUser(userId);
        return user != null ? user.getName() : null;
    }

    protected String getUserMobile(Long userId) {
        if (userId == null) {
            return null;
        }
        MemberUserDO user = memberUserService.getUser(userId);
        return user != null ? user.getMobile() : null;
    }

    protected String getAuditUserName(Long auditUserId) {
        if (auditUserId == null) {
            return null;
        }
        AdminUserRespDTO adminUser = adminUserApi.getUser(auditUserId);
        if (adminUser == null) {
            return null;
        }
        // 返回格式：昵称(手机号)
        String nickname = adminUser.getNickname();
        if (StrUtil.isEmpty(adminUser.getMobile())) {
            return nickname;
        }
        return StrUtil.format("{}({})", nickname, adminUser.getMobile());
    }

}
