package cn.iocoder.yudao.module.member.convert.auditrecord;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 会员审核记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberAuditRecordConvert {

    MemberAuditRecordConvert INSTANCE = Mappers.getMapper(MemberAuditRecordConvert.class);

    MemberAuditRecordDO convert(MemberAuditRecordCreateReqVO bean);

    @Mapping(target = "auditStatusName", expression = "java(getAuditStatusName(bean.getAuditStatus()))")
    @Mapping(target = "beforeStatusName", expression = "java(getAuditStatusName(bean.getBeforeStatus()))")
    @Mapping(target = "afterStatusName", expression = "java(getAuditStatusName(bean.getAfterStatus()))")
    @Mapping(target = "userName", ignore = true)
    @Mapping(target = "userMobile", ignore = true)
    @Mapping(target = "auditUserName", source = "auditUserName")
    MemberAuditRecordRespVO convert(MemberAuditRecordDO bean);

    List<MemberAuditRecordRespVO> convertList(List<MemberAuditRecordDO> list);

    PageResult<MemberAuditRecordRespVO> convertPage(PageResult<MemberAuditRecordDO> page);

    default String getAuditStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        MemberAuditStatusEnum statusEnum = MemberAuditStatusEnum.getByStatus(status);
        return statusEnum != null ? statusEnum.getName() : null;
    }

}
