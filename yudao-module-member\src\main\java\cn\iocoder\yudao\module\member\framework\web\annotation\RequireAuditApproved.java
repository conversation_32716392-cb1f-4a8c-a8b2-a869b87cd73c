package cn.iocoder.yudao.module.member.framework.web.annotation;

import java.lang.annotation.*;

/**
 * 要求用户审核通过的注解
 * 
 * 用于标记需要用户审核通过才能访问的接口
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireAuditApproved {

    /**
     * 是否必须审核通过
     * 
     * @return 默认为 true，表示必须审核通过
     */
    boolean value() default true;

}
