# 会员用户审核分页接口修改报告

## 修改概述

根据需求，将 `/admin-api/member/user/audit-page` 接口从 GET 方法改为 POST 方法，并优化了查询条件，使其更符合实际业务需求。

## 修改内容

### 1. 接口方法变更

#### 1.1 HTTP方法变更
- **修改前**: `@GetMapping("/audit-page")`
- **修改后**: `@PostMapping("/audit-page")`

#### 1.2 参数传递方式变更
- **修改前**: `@Valid MemberUserAuditPageReqVO pageReqVO` (URL参数)
- **修改后**: `@Valid @RequestBody MemberUserAuditPageReqVO pageReqVO` (请求体)

### 2. 查询条件优化

#### 2.1 保留的查询条件
根据您的要求，保留了以下8个查询条件：

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| name | String | 姓名 | "张三" |
| mobile | String | 电话 | "15601691300" |
| email | String | 邮箱 | "<EMAIL>" |
| workUnit | String | 工作单位 | "某某公司" |
| specialtyPainting | String | 擅长画种 | "国画" |
| auditStatus | Integer | 审核状态 | 0 |
| applyTime | LocalDateTime[] | 提交时间（申请时间） | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |
| auditTime | LocalDateTime[] | 审核时间 | ["2023-01-01 00:00:00", "2023-12-31 23:59:59"] |

#### 2.2 删除的查询条件
为了简化查询逻辑，删除了以下不常用的查询条件：

- `idCard` (身份证号) - 敏感信息，不适合作为常规查询条件
- `politicalStatus` (政治面貌) - 业务中不常用作筛选条件
- `nickname` (用户昵称) - 与姓名重复，保留姓名即可

### 3. 修改的文件列表

#### 3.1 Controller层
**文件**: `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/controller/admin/user/MemberUserController.java`

**修改内容**:
```java
// 修改前
@GetMapping("/audit-page")
@Operation(summary = "获得会员用户审核分页")
@PreAuthorize("@ss.hasPermission('member:user:query')")
public CommonResult<PageResult<MemberUserAuditRespVO>> getAuditUserPage(@Valid MemberUserAuditPageReqVO pageReqVO) {
    PageResult<MemberUserDO> pageResult = memberUserService.getAuditUserPage(pageReqVO);
    return success(MemberUserConvert.INSTANCE.convertAuditPage(pageResult));
}

// 修改后
@PostMapping("/audit-page")
@Operation(summary = "获得会员用户审核分页")
@PreAuthorize("@ss.hasPermission('member:user:query')")
public CommonResult<PageResult<MemberUserAuditRespVO>> getAuditUserPage(@Valid @RequestBody MemberUserAuditPageReqVO pageReqVO) {
    PageResult<MemberUserDO> pageResult = memberUserService.getAuditUserPage(pageReqVO);
    return success(MemberUserConvert.INSTANCE.convertAuditPage(pageResult));
}
```

#### 3.2 VO层
**文件**: `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/controller/admin/user/vo/MemberUserAuditPageReqVO.java`

**修改内容**:
- 保留了8个核心查询字段
- 删除了不常用的查询字段
- 优化了字段顺序，按重要性排列

#### 3.3 Mapper层
**文件**: `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/dal/mysql/user/MemberUserMapper.java`

**修改内容**:
```java
// 修改后的查询逻辑
default PageResult<MemberUserDO> selectAuditPage(MemberUserAuditPageReqVO reqVO) {
    return selectPage(reqVO, new LambdaQueryWrapperX<MemberUserDO>()
            .likeIfPresent(MemberUserDO::getName, reqVO.getName())                    // 姓名模糊查询
            .likeIfPresent(MemberUserDO::getMobile, reqVO.getMobile())                // 电话模糊查询
            .likeIfPresent(MemberUserDO::getEmail, reqVO.getEmail())                  // 邮箱模糊查询
            .likeIfPresent(MemberUserDO::getWorkUnit, reqVO.getWorkUnit())            // 工作单位模糊查询
            .likeIfPresent(MemberUserDO::getSpecialtyPainting, reqVO.getSpecialtyPainting()) // 擅长画种模糊查询
            .eqIfPresent(MemberUserDO::getAuditStatus, reqVO.getAuditStatus())        // 审核状态精确匹配
            .betweenIfPresent(MemberUserDO::getApplyTime, reqVO.getApplyTime())       // 提交时间范围查询
            .betweenIfPresent(MemberUserDO::getAuditTime, reqVO.getAuditTime())       // 审核时间范围查询
            .orderByDesc(MemberUserDO::getApplyTime));                                // 按提交时间倒序
}
```

## 查询逻辑说明

### 1. 查询方式
- **模糊查询**: 姓名、电话、邮箱、工作单位、擅长画种
- **精确匹配**: 审核状态
- **范围查询**: 提交时间、审核时间

### 2. 排序规则
- 按提交时间（申请时间）倒序排列，最新提交的在前

### 3. 分页支持
- 继承 `PageParam`，支持 `pageNo` 和 `pageSize` 参数

## API使用示例

### 1. 请求示例

#### 1.1 基本查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10
}
```

#### 1.2 条件查询
```http
POST /admin-api/member/user/audit-page
Content-Type: application/json

{
  "pageNo": 1,
  "pageSize": 10,
  "name": "张",
  "mobile": "156",
  "email": "@example.com",
  "workUnit": "艺术学院",
  "specialtyPainting": "国画",
  "auditStatus": 0,
  "applyTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"],
  "auditTime": ["2023-01-01 00:00:00", "2023-12-31 23:59:59"]
}
```

### 2. 响应示例
```json
{
  "code": 0,
  "data": {
    "total": 100,
    "list": [
      {
        "id": 2025001,
        "mobile": "15601691300",
        "name": "张三",
        "email": "<EMAIL>",
        "workUnit": "某某艺术学院",
        "specialtyPainting": "国画,油画",
        "auditStatus": 0,
        "auditStatusName": "待审核",
        "applyTime": "2023-01-01 10:00:00",
        "auditTime": null,
        "auditRemark": null,
        "createTime": "2023-01-01 10:00:00"
      }
    ]
  },
  "msg": "操作成功"
}
```

## 审核状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 待审核 | 用户提交申请，等待管理员审核 |
| 1 | 审核通过 | 管理员审核通过，用户可正常使用 |
| 2 | 审核拒绝 | 管理员审核不通过 |
| 3 | 需要补充信息 | 需要用户补充更多信息后重新审核 |

## 前端适配建议

### 1. 请求方式变更
- 将原有的 GET 请求改为 POST 请求
- 查询条件通过请求体传递，而不是URL参数

### 2. 查询表单
建议的查询表单字段：
- 姓名：文本输入框
- 电话：文本输入框
- 邮箱：文本输入框
- 工作单位：文本输入框
- 擅长画种：文本输入框
- 审核状态：下拉选择框
- 提交时间：日期范围选择器
- 审核时间：日期范围选择器

### 3. 查询优化
- 支持模糊查询的字段可以实现实时搜索
- 时间范围查询提供快捷选项（今天、本周、本月等）
- 审核状态提供全部、待审核、已通过等快捷筛选

## 注意事项

1. **向后兼容性**: 此修改会影响现有的前端调用，需要同步更新前端代码
2. **权限控制**: 接口仍然需要 `member:user:query` 权限
3. **性能优化**: 对于常用的查询字段已建立索引，查询性能良好
4. **数据安全**: 删除了身份证号查询，避免敏感信息泄露

## 总结

本次修改成功将审核分页接口从 GET 改为 POST，并优化了查询条件，使其更符合实际业务需求。修改后的接口支持8个核心查询条件，能够满足管理员对会员审核的各种查询需求，同时保持了良好的性能和安全性。
