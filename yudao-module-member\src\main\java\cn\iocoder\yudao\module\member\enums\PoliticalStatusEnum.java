package cn.iocoder.yudao.module.member.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 政治面貌枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PoliticalStatusEnum {

    /**
     * 群众
     */
    MASSES(1, "群众"),

    /**
     * 共青团员
     */
    YOUTH_LEAGUE_MEMBER(2, "共青团员"),

    /**
     * 中共党员
     */
    CPC_MEMBER(3, "中共党员"),

    /**
     * 中共预备党员
     */
    CPC_PROBATIONARY_MEMBER(4, "中共预备党员"),

    /**
     * 民革党员
     */
    REVOLUTIONARY_COMMITTEE_MEMBER(5, "民革党员"),

    /**
     * 民盟盟员
     */
    DEMOCRATIC_LEAGUE_MEMBER(6, "民盟盟员"),

    /**
     * 民建会员
     */
    DEMOCRATIC_NATIONAL_CONSTRUCTION_MEMBER(7, "民建会员"),

    /**
     * 民进会员
     */
    DEMOCRATIC_PROGRESSIVE_PARTY_MEMBER(8, "民进会员"),

    /**
     * 农工党党员
     */
    PEASANTS_AND_WORKERS_PARTY_MEMBER(9, "农工党党员"),

    /**
     * 致公党党员
     */
    ZHINONG_PARTY_MEMBER(10, "致公党党员"),

    /**
     * 九三学社社员
     */
    JIUSAN_SOCIETY_MEMBER(11, "九三学社社员"),

    /**
     * 台盟盟员
     */
    TAIWAN_DEMOCRATIC_SELF_GOVERNMENT_LEAGUE_MEMBER(12, "台盟盟员"),

    /**
     * 无党派人士
     */
    NON_PARTY_PERSONAGE(13, "无党派人士");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static PoliticalStatusEnum getByStatus(Integer status) {
        for (PoliticalStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 状态名称
     * @return 枚举
     */
    public static PoliticalStatusEnum getByName(String name) {
        for (PoliticalStatusEnum statusEnum : values()) {
            if (statusEnum.getName().equals(name)) {
                return statusEnum;
            }
        }
        return null;
    }
}
