package cn.iocoder.yudao.module.member.service.auditrecord;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.convert.auditrecord.MemberAuditRecordConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.dal.mysql.auditrecord.MemberAuditRecordMapper;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MemberAuditRecordServiceImpl implements MemberAuditRecordService {

    @Resource
    private MemberAuditRecordMapper auditRecordMapper;

    @Resource
    private MemberUserService memberUserService;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createAuditRecord(@Valid MemberAuditRecordCreateReqVO createReqVO) {
        // 插入
        MemberAuditRecordDO auditRecord = MemberAuditRecordConvert.INSTANCE.convert(createReqVO);
        auditRecordMapper.insert(auditRecord);
        // 返回
        return auditRecord.getId();
    }

    @Override
    public PageResult<MemberAuditRecordRespVO> getAuditRecordPage(MemberAuditRecordPageReqVO pageReqVO) {
        PageResult<MemberAuditRecordDO> pageResult = auditRecordMapper.selectPage(pageReqVO);
        return convertToRespVOPage(pageResult);
    }

    @Override
    public List<MemberAuditRecordRespVO> getAuditRecordListByUserId(Long userId) {
        List<MemberAuditRecordDO> list = auditRecordMapper.selectListByUserId(userId);
        return convertToRespVOList(list);
    }

    private PageResult<MemberAuditRecordRespVO> convertToRespVOPage(PageResult<MemberAuditRecordDO> pageResult) {
        List<MemberAuditRecordRespVO> respVOList = convertToRespVOList(pageResult.getList());
        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    private List<MemberAuditRecordRespVO> convertToRespVOList(List<MemberAuditRecordDO> list) {
        List<MemberAuditRecordRespVO> respVOList = MemberAuditRecordConvert.INSTANCE.convertList(list);

        // 填充用户信息和审核人信息
        for (MemberAuditRecordRespVO respVO : respVOList) {
            // 填充用户信息
            if (respVO.getUserId() != null) {
                MemberUserDO user = memberUserService.getUser(respVO.getUserId());
                if (user != null) {
                    respVO.setUserName(user.getName());
                    respVO.setUserMobile(user.getMobile());
                }
            }

            // 填充审核人信息（如果数据库中没有保存审核人姓名）
            if (StrUtil.isEmpty(respVO.getAuditUserName()) && respVO.getAuditUserId() != null) {
                AdminUserRespDTO adminUser = adminUserApi.getUser(respVO.getAuditUserId());
                if (adminUser != null) {
                    String nickname = adminUser.getNickname();
                    if (StrUtil.isEmpty(adminUser.getMobile())) {
                        respVO.setAuditUserName(nickname);
                    } else {
                        respVO.setAuditUserName(StrUtil.format("{}({})", nickname, adminUser.getMobile()));
                    }
                }
            }
        }

        return respVOList;
    }

}
