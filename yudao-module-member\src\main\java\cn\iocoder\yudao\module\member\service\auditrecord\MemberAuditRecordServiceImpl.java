package cn.iocoder.yudao.module.member.service.auditrecord;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.convert.auditrecord.MemberAuditRecordConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.dal.mysql.auditrecord.MemberAuditRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MemberAuditRecordServiceImpl implements MemberAuditRecordService {

    @Resource
    private MemberAuditRecordMapper auditRecordMapper;

    @Override
    public Long createAuditRecord(@Valid MemberAuditRecordCreateReqVO createReqVO) {
        // 插入
        MemberAuditRecordDO auditRecord = MemberAuditRecordConvert.INSTANCE.convert(createReqVO);
        auditRecordMapper.insert(auditRecord);
        // 返回
        return auditRecord.getId();
    }

    @Override
    public PageResult<MemberAuditRecordDO> getAuditRecordPage(MemberAuditRecordPageReqVO pageReqVO) {
        return auditRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemberAuditRecordDO> getAuditRecordListByUserId(Long userId) {
        return auditRecordMapper.selectListByUserId(userId);
    }

}
