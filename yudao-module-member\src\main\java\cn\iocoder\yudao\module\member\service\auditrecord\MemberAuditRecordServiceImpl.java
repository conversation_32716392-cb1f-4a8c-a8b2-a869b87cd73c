package cn.iocoder.yudao.module.member.service.auditrecord;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordCreateReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordPageReqVO;
import cn.iocoder.yudao.module.member.controller.admin.auditrecord.vo.MemberAuditRecordRespVO;
import cn.iocoder.yudao.module.member.convert.auditrecord.MemberAuditRecordConvert;
import cn.iocoder.yudao.module.member.dal.dataobject.auditrecord.MemberAuditRecordDO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.dal.mysql.auditrecord.MemberAuditRecordMapper;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MemberAuditRecordServiceImpl implements MemberAuditRecordService {

    @Resource
    private MemberAuditRecordMapper auditRecordMapper;

    @Resource
    private MemberUserService memberUserService;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createAuditRecord(@Valid MemberAuditRecordCreateReqVO createReqVO) {
        // 插入
        MemberAuditRecordDO auditRecord = MemberAuditRecordConvert.INSTANCE.convert(createReqVO);
        auditRecordMapper.insert(auditRecord);
        // 返回
        return auditRecord.getId();
    }

    @Override
    public PageResult<MemberAuditRecordRespVO> getAuditRecordPage(MemberAuditRecordPageReqVO pageReqVO) {
        // 如果有用户姓名或手机号查询条件，先查询符合条件的用户ID
        if (StrUtil.isNotBlank(pageReqVO.getUserName()) || StrUtil.isNotBlank(pageReqVO.getUserMobile())) {
            // 创建新的查询条件，先查询用户
            MemberAuditRecordPageReqVO newPageReqVO = new MemberAuditRecordPageReqVO();
            newPageReqVO.setPageNo(pageReqVO.getPageNo());
            newPageReqVO.setPageSize(pageReqVO.getPageSize());
            newPageReqVO.setAuditStatus(pageReqVO.getAuditStatus());
            newPageReqVO.setAuditUserId(pageReqVO.getAuditUserId());
            newPageReqVO.setAuditTime(pageReqVO.getAuditTime());

            // 查询符合条件的用户ID
            List<Long> userIds = memberUserService.getUserIdsByNameOrMobile(pageReqVO.getUserName(), pageReqVO.getUserMobile());
            if (userIds.isEmpty()) {
                // 如果没有符合条件的用户，返回空结果
                return new PageResult<>(List.of(), 0L);
            }
z
            // 分页查询审核记录，按用户ID过滤
            PageResult<MemberAuditRecordDO> pageResult = auditRecordMapper.selectPageByUserIds(newPageReqVO, userIds);
            return convertToRespVOPage(pageResult);
        } else {
            PageResult<MemberAuditRecordDO> pageResult = auditRecordMapper.selectPage(pageReqVO);
            return convertToRespVOPage(pageResult);
        }
    }

    @Override
    public List<MemberAuditRecordRespVO> getAuditRecordListByUserId(Long userId) {
        List<MemberAuditRecordDO> list = auditRecordMapper.selectListByUserId(userId);
        return convertToRespVOList(list);
    }

    private PageResult<MemberAuditRecordRespVO> convertToRespVOPage(PageResult<MemberAuditRecordDO> pageResult) {
        List<MemberAuditRecordRespVO> respVOList = convertToRespVOList(pageResult.getList());
        return new PageResult<>(respVOList, pageResult.getTotal());
    }

    private List<MemberAuditRecordRespVO> convertToRespVOList(List<MemberAuditRecordDO> list) {
        List<MemberAuditRecordRespVO> respVOList = MemberAuditRecordConvert.INSTANCE.convertList(list);

        // 填充用户信息和审核人信息
        for (MemberAuditRecordRespVO respVO : respVOList) {
            // 填充用户信息
            if (respVO.getUserId() != null) {
                MemberUserDO user = memberUserService.getUser(respVO.getUserId());
                if (user != null) {
                    respVO.setUserName(user.getName());
                    respVO.setUserMobile(user.getMobile());
                }
            }

            // 填充审核人信息（如果数据库中没有保存审核人姓名）
            if (StrUtil.isEmpty(respVO.getAuditUserName()) && respVO.getAuditUserId() != null) {
                AdminUserRespDTO adminUser = adminUserApi.getUser(respVO.getAuditUserId());
                if (adminUser != null) {
                    String nickname = adminUser.getNickname();
                    if (StrUtil.isEmpty(adminUser.getMobile())) {
                        respVO.setAuditUserName(nickname);
                    } else {
                        respVO.setAuditUserName(StrUtil.format("{}({})", nickname, adminUser.getMobile()));
                    }
                }
            }
        }

        return respVOList;
    }

}
