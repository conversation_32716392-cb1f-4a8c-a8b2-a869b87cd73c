-- ========================================
-- 会员用户表(member_user)完整结构脚本
-- 更新日期: 2025-09-09
-- 说明: 重新定义member_user表结构，包含所有必需字段
-- ========================================

-- 1. 删除现有表（如果存在）- 谨慎使用
DROP TABLE IF EXISTS `member_user`;

-- 2. 创建完整的member_user表
CREATE TABLE IF NOT EXISTS `member_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会员卡号（自增）',
  `mobile` varchar(11) NOT NULL COMMENT '电话',
  `email` varchar(100) NULL COMMENT '邮箱',
  `password` varchar(100) NOT NULL COMMENT '密码（加密存储）',
  `name` varchar(30) NULL COMMENT '姓名',
  `id_card` varchar(255) NULL COMMENT '身份证号（加密存储）',
  `political_status` tinyint NULL COMMENT '政治面貌：1-群众，2-共青团员，3-中共党员，4-中共预备党员，5-民革党员，6-民盟盟员，7-民建会员，8-民进会员，9-农工党党员，10-致公党党员，11-九三学社社员，12-台盟盟员，13-无党派人士',
  `birthday` datetime NULL COMMENT '生日',
  `work_unit` varchar(200) NULL COMMENT '工作单位',
  `specialty_painting` varchar(500) NULL COMMENT '擅长画种',
  `audit_status` tinyint NULL COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息',
  `audit_time` datetime NULL COMMENT '审核时间',
  `audit_user_id` bigint NULL COMMENT '审核人ID',
  `audit_remark` varchar(500) NULL COMMENT '审核备注',
  `pay_time` datetime NULL COMMENT '交费时间',
  `expire_time` datetime NULL COMMENT '过期时间',
  `login_status` tinyint NOT NULL DEFAULT 0 COMMENT '登录状态：0-离线，1-在线',
  
  -- 系统字段
  `nickname` varchar(30) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像',
  `status` tinyint NOT NULL COMMENT '账号状态：0-禁用，1-启用',
  `sex` tinyint NULL COMMENT '性别：1-男，2-女',
  `area_id` int NULL COMMENT '所在地',
  `mark` varchar(255) NULL COMMENT '备注',
  `point` int DEFAULT 0 NULL COMMENT '积分',
  `register_ip` varchar(32) NOT NULL COMMENT '注册IP',
  `register_terminal` tinyint NULL COMMENT '注册终端',
  `login_ip` varchar(50) NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `tag_ids` varchar(255) NULL DEFAULT NULL COMMENT '用户标签编号列表,以逗号分隔',
  `level_id` bigint NULL DEFAULT NULL COMMENT '等级编号',
  `experience` bigint NULL DEFAULT NULL COMMENT '经验',
  `group_id` bigint NULL DEFAULT NULL COMMENT '用户分组编号',
  `apply_time` datetime NULL COMMENT '申请时间（注册时间）',
  
  -- 通用字段
  `creator` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_mobile` (`mobile`) USING BTREE COMMENT '手机号唯一索引',
  KEY `idx_email` (`email`) USING BTREE COMMENT '邮箱索引',
  KEY `idx_political_status` (`political_status`) USING BTREE COMMENT '政治面貌索引',
  KEY `idx_audit_status` (`audit_status`) USING BTREE COMMENT '审核状态索引',
  KEY `idx_login_status` (`login_status`) USING BTREE COMMENT '登录状态索引',
  KEY `idx_work_unit` (`work_unit`) USING BTREE COMMENT '工作单位索引',
  KEY `idx_specialty_painting` (`specialty_painting`) USING BTREE COMMENT '擅长画种索引',
  KEY `idx_expire_time` (`expire_time`) USING BTREE COMMENT '过期时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=20250000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员表';
