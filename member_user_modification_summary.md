# 会员用户表(member_user)修改总结

## 修改概述

根据新需求，对member_user表进行了结构调整，新增了以下字段：
- 会员卡号（自增）- 使用原有的id字段
- 姓名 - 使用原有的name字段
- 身份证号 - 新增字段
- 政治面貌 - 新增字段
- 电话 - 使用原有的mobile字段
- 邮箱 - 新增字段
- 生日 - 使用原有的birthday字段
- 密码 - 使用原有的password字段
- 审核状态 - 已存在字段
- 审核时间 - 已存在字段
- 交费时间 - 新增字段
- 过期时间 - 新增字段
- 登录状态 - 新增字段
- 备注 - 使用原有的mark字段

## 修改的文件列表

### 1. 数据库相关文件

#### 1.1 SQL脚本
- `sql/mysql/member_user_update.sql` - 新建，包含表结构更新脚本
- `yudao-module-member/src/test/resources/sql/create_tables.sql` - 更新测试用建表语句

#### 1.2 实体类
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/dal/dataobject/user/MemberUserDO.java` - 更新实体类，添加新字段

#### 1.3 Mapper接口
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/dal/mysql/user/MemberUserMapper.java` - 更新Mapper，添加新的查询方法

### 2. 控制层相关文件

#### 2.1 请求VO
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/controller/admin/user/vo/MemberUserPageReqVO.java` - 更新分页查询请求VO
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/controller/admin/user/vo/MemberUserAuditPageReqVO.java` - 更新审核分页查询请求VO

### 3. 枚举类

#### 3.1 新增枚举
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/PoliticalStatusEnum.java` - 新建，政治面貌枚举
- `yudao-module-member/src/main/java/cn/iocoder/yudao/module/member/enums/MemberLoginStatusEnum.java` - 新建，登录状态枚举

## 新增字段详情

### 数据库字段

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 注释 |
|--------|------|------|----------|--------|------|
| id_card | varchar | 18 | YES | NULL | 身份证号 |
| political_status | varchar | 50 | YES | NULL | 政治面貌 |
| email | varchar | 100 | YES | NULL | 邮箱 |
| login_status | tinyint | - | NO | 0 | 登录状态：0-离线，1-在线 |
| audit_status | tinyint | - | YES | NULL | 审核状态：0-待审核，1-审核通过，2-审核拒绝，3-需要补充信息 |
| audit_time | datetime | - | YES | NULL | 审核时间 |
| audit_user_id | bigint | - | YES | NULL | 审核人ID |
| audit_remark | varchar | 500 | YES | NULL | 审核备注 |
| apply_time | datetime | - | YES | NULL | 申请时间（注册时间） |
| pay_time | datetime | - | YES | NULL | 交费时间 |
| expire_time | datetime | - | YES | NULL | 过期时间 |

### 索引

| 索引名 | 类型 | 字段 | 注释 |
|--------|------|------|------|
| uk_id_card | UNIQUE | id_card | 身份证号唯一索引 |
| idx_email | INDEX | email | 邮箱索引 |
| idx_audit_status | INDEX | audit_status | 审核状态索引 |
| idx_expire_time | INDEX | expire_time | 过期时间索引 |

## 新增的Mapper方法

### MemberUserMapper新增方法

1. `selectByEmail(String email)` - 根据邮箱查询用户
2. `selectByIdCard(String idCard)` - 根据身份证号查询用户
3. `selectListByExpireTime(LocalDateTime startTime, LocalDateTime endTime)` - 根据过期时间查询即将过期的会员
4. `selectListByLoginStatus(Integer loginStatus)` - 根据登录状态查询会员

### 更新的查询方法

1. `selectPage()` - 增加了对新字段的查询支持
2. `selectAuditPage()` - 增加了对邮箱和身份证号的查询支持

## 枚举类说明

### PoliticalStatusEnum（政治面貌枚举）
包含常见的政治面貌选项：
- 群众
- 共青团员
- 中共党员
- 中共预备党员
- 各民主党派成员等

### MemberLoginStatusEnum（登录状态枚举）
- OFFLINE(0, "离线")
- ONLINE(1, "在线")

## 使用说明

### 1. 数据库更新
执行 `sql/mysql/member_user_update.sql` 脚本来更新现有数据库结构。

### 2. 代码使用
更新后的实体类和Mapper可以支持新字段的CRUD操作，包括：
- 根据身份证号、邮箱查询用户
- 分页查询时支持新字段的筛选
- 审核相关功能的完整支持
- 会员过期时间管理
- 登录状态管理

### 3. 注意事项
- 身份证号字段设置了唯一索引，确保数据唯一性
- 审核相关字段已经在原有代码中存在，此次主要是完善了数据库结构
- 新增的枚举类可以在前端下拉选择中使用
- 过期时间字段可以用于会员到期提醒功能

## 后续建议

1. 可以考虑添加数据验证注解，如身份证号格式验证
2. 可以添加邮箱格式验证
3. 可以考虑添加会员到期提醒的定时任务
4. 可以添加登录状态的自动更新机制
