package cn.iocoder.yudao.module.member.mq.consumer.user;

import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.enums.MemberAuditStatusEnum;
import cn.iocoder.yudao.module.member.mq.message.user.MemberUserAuditMessage;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员用户审核消息消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class MemberUserAuditConsumer {

    @Resource
    private MemberUserService memberUserService;

    @EventListener
    public void onMessage(MemberUserAuditMessage message) {
        log.info("[onMessage][收到会员用户审核消息：{}]", message);
        
        try {
            // 获取用户信息
            MemberUserDO user = memberUserService.getUser(message.getUserId());
            if (user == null) {
                log.warn("[onMessage][用户不存在，userId：{}]", message.getUserId());
                return;
            }
            
            // 根据审核状态发送不同的通知
            MemberAuditStatusEnum auditStatus = MemberAuditStatusEnum.getByStatus(message.getAuditStatus());
            if (auditStatus == null) {
                log.warn("[onMessage][审核状态无效，auditStatus：{}]", message.getAuditStatus());
                return;
            }
            
            switch (auditStatus) {
                case APPROVED:
                    sendApprovedNotification(user, message.getAuditRemark());
                    break;
                case REJECTED:
                    sendRejectedNotification(user, message.getAuditRemark());
                    break;
                case NEED_SUPPLEMENT:
                    sendSupplementNotification(user, message.getAuditRemark());
                    break;
                default:
                    log.info("[onMessage][无需发送通知，审核状态：{}]", auditStatus.getName());
                    break;
            }
        } catch (Exception e) {
            log.error("[onMessage][处理会员用户审核消息异常，message：{}]", message, e);
        }
    }

    /**
     * 发送审核通过通知
     */
    private void sendApprovedNotification(MemberUserDO user, String auditRemark) {
        log.info("[sendApprovedNotification][发送审核通过通知，userId：{}，mobile：{}]", user.getId(), user.getMobile());
        // TODO: 实现具体的通知逻辑，比如：
        // 1. 发送短信通知
        // 2. 发送站内消息
        // 3. 发送邮件通知
        // 4. 推送APP消息
    }

    /**
     * 发送审核拒绝通知
     */
    private void sendRejectedNotification(MemberUserDO user, String auditRemark) {
        log.info("[sendRejectedNotification][发送审核拒绝通知，userId：{}，mobile：{}，原因：{}]", 
                user.getId(), user.getMobile(), auditRemark);
        // TODO: 实现具体的通知逻辑
    }

    /**
     * 发送补充信息通知
     */
    private void sendSupplementNotification(MemberUserDO user, String auditRemark) {
        log.info("[sendSupplementNotification][发送补充信息通知，userId：{}，mobile：{}，要求：{}]", 
                user.getId(), user.getMobile(), auditRemark);
        // TODO: 实现具体的通知逻辑
    }

}
